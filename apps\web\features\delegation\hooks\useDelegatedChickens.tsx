"use client";

import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useStateContext } from "@/providers/app/state";
import { DelegationAPI } from "../api/delegation.api";
import { IChickenDelegationInfo } from "../types/delegation.types";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IAttribute, IChickenMetadata } from "@/lib/types/chicken.types";
import { fetchGameStats, transformToBattleStats } from "@/lib/api/game-api";

// Extended interface for delegated chickens that includes delegation info
export interface IDelegatedChicken {
  tokenId: number;
  image: string;
  metadata?: IChickenMetadata;
  dailyFeathers?: number;
  breedCount?: number;
  type?: string;
  level?: number;
  winRate?: number;
  // Delegation specific fields
  delegatedTask: number;
  rewardDistribution: number;
  sharedRewardAmount: number | null;
  renterAddress: string;
  ownerAddress: string;
  legendaryCount: number;
  isDelegated: true; // Always true for delegated chickens
}

// Interface for battle stats
interface IBattleStats {
  wins: number;
  losses: number;
  level?: number;
  state?: string; // "normal", "faint", "dead", "breeding"
  recoverDate?: string;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

const fetchChickenBattleStats = async (
  tokenId: number
): Promise<IBattleStats> => {
  const gameStats = await fetchGameStats(tokenId);
  return transformToBattleStats(gameStats);
};

export function useDelegatedChickens() {
  const { address, isConnected } = useStateContext();

  // Fetch delegated chickens for current user (as delegatee/renter)
  const delegatedChickensQuery = useQuery({
    queryKey: ["delegatedChickens", address],
    queryFn: async () => {
      if (!address) return [];
      const response = await DelegationAPI.getChickensByWallet(address);
      return response.data || [];
    },
    enabled: !!address && isConnected,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Extract token IDs from delegated chickens
  const delegatedTokenIds = useMemo(() => {
    return delegatedChickensQuery.isSuccess
      ? delegatedChickensQuery.data.map((chicken) => chicken.tokenId)
      : [];
  }, [delegatedChickensQuery.isSuccess, delegatedChickensQuery.data]);

  // Fetch metadata for delegated chickens
  const metadataQuery = useChickenMetadata(delegatedTokenIds);

  // Fetch battle stats for delegated chickens
  const battleStatsQuery = useQuery({
    queryKey: ["delegatedChickensBattleStats", delegatedTokenIds],
    queryFn: async () => {
      if (delegatedTokenIds.length === 0) return {};

      const statsPromises = delegatedTokenIds.map(async (tokenId) => {
        const stats = await fetchChickenBattleStats(tokenId);
        return { tokenId, stats };
      });

      const results = await Promise.allSettled(statsPromises);
      const statsMap: Record<number, IBattleStats> = {};

      results.forEach((result, index) => {
        const tokenId = delegatedTokenIds[index];
        if (tokenId !== undefined) {
          if (result.status === "fulfilled") {
            statsMap[tokenId] = result.value.stats;
          } else {
            // Default stats if fetch failed
            statsMap[tokenId] = {
              wins: 0,
              losses: 0,
            };
          }
        }
      });

      return statsMap;
    },
    enabled: delegatedTokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Transform delegated chickens data to match inventory interface
  const delegatedChickens = useMemo(() => {
    if (
      !delegatedChickensQuery.isSuccess ||
      !metadataQuery.metadataQuery.isSuccess
    ) {
      return [];
    }

    const metadataMap = metadataQuery.metadataMap;
    const battleStatsMap = battleStatsQuery.data || {};

    return delegatedChickensQuery.data.map(
      (delegationInfo: IChickenDelegationInfo) => {
        const chickenMetadata = metadataMap[delegationInfo.tokenId];
        const battleStats = battleStatsMap[delegationInfo.tokenId] || {
          wins: 0,
          losses: 0,
        };

        // Extract attributes from metadata
        const typeAttribute = chickenMetadata?.attributes.find(
          (attr: IAttribute) => attr.trait_type === "Type"
        );
        const levelAttribute = chickenMetadata?.attributes.find(
          (attr: IAttribute) => attr.trait_type === "Level"
        );
        const breedCountAttribute = chickenMetadata?.attributes.find(
          (attr: IAttribute) => attr.trait_type === "Breed Count"
        );

        // Calculate win rate from battle stats
        const totalBattles = battleStats.wins + battleStats.losses;
        const winRate =
          totalBattles > 0
            ? Math.round((battleStats.wins / totalBattles) * 100)
            : 0;

        const delegatedChicken: IDelegatedChicken = {
          tokenId: delegationInfo.tokenId,
          image:
            delegationInfo.image ||
            chickenMetadata?.image ||
            `https://chicken-api-ivory.vercel.app/api/image/${delegationInfo.tokenId}.png`,
          metadata: chickenMetadata,
          type: typeAttribute?.value as string,
          dailyFeathers: delegationInfo.dailyFeathers || 0,
          level: Number(levelAttribute?.value) || 1,
          breedCount: Number(breedCountAttribute?.value) || 0,
          winRate,
          // Delegation specific fields
          delegatedTask: delegationInfo.delegatedTask,
          rewardDistribution: delegationInfo.rewardDistribution,
          sharedRewardAmount: delegationInfo.sharedRewardAmount,
          renterAddress: delegationInfo.renterAddress,
          ownerAddress: delegationInfo.ownerAddress,
          legendaryCount: delegationInfo.legendaryCount,
          isDelegated: true,
        };

        return delegatedChicken;
      }
    );
  }, [
    delegatedChickensQuery.isSuccess,
    delegatedChickensQuery.data,
    metadataQuery.metadataQuery.isSuccess,
    metadataQuery.metadataMap,
    battleStatsQuery.data,
  ]);

  return {
    delegatedChickens,
    isLoading:
      delegatedChickensQuery.isLoading || metadataQuery.metadataQuery.isLoading,
    isFetching:
      delegatedChickensQuery.isFetching ||
      metadataQuery.metadataQuery.isFetching,
    error: delegatedChickensQuery.error || metadataQuery.metadataQuery.error,
    isConnected,
  };
}
