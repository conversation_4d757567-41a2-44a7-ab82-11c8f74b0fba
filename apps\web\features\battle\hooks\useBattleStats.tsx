"use client";

import { useQuery } from "@tanstack/react-query";
import { IBattleStats } from "../types/battle.types";
import {
  fetchGameStats,
  fetchGameStatsBatch,
  transformToBattleStats,
  transformToBattleStatsMap,
} from "@/lib/api/game-api";

/**
 * Fetch battle stats for a single chicken
 */
const fetchChickenBattleStats = async (
  tokenId: number
): Promise<IBattleStats> => {
  const gameStats = await fetchGameStats(tokenId);
  return transformToBattleStats(gameStats);
};

/**
 * Hook to fetch battle stats for a single chicken
 */
export const useBattleStats = (tokenId: number | null) => {
  return useQuery({
    queryKey: ["battleStats", tokenId],
    queryFn: () => fetchChickenBattleStats(tokenId!),
    enabled: !!tokenId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook to fetch battle stats for multiple chickens
 */
export const useBulkBattleStats = (tokenIds: number[]) => {
  return useQuery({
    queryKey: ["bulkBattleStats", tokenIds],
    queryFn: async () => {
      if (tokenIds.length === 0) return {};

      // Use batch API for better performance
      const gameStatsMap = await fetchGameStatsBatch(tokenIds);
      return transformToBattleStatsMap(gameStatsMap);
    },
    enabled: tokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Export the fetch function for direct use
 */
export { fetchChickenBattleStats };
