/**
 * Game API utilities for fetching chicken battle stats and game data
 * Provides both individual and batch operations with proper error handling
 */

export interface IGameStats {
  id: number;
  wins?: number;
  losses?: number;
  draws?: number;
  level?: number;
  state?: string;
  recoverDate?: string;
  breedingTime?: number;
  hpCooldown?: number;
  regenRate?: number;
  stats?: {
    currentHp?: number;
    hp?: number;
    attack?: number;
    defense?: number;
    speed?: number;
  };
}

export interface IBatchGameResponse {
  chickens: IGameStats[];
}

export interface IBattleStats {
  wins: number;
  losses: number;
  draws?: number;
  level?: number;
  state: string;
  recoverDate?: string;
  stats?: any;
}

/**
 * Fetch game stats for multiple chickens using the batch endpoint
 * @param tokenIds - Array of chicken token IDs
 * @returns Promise with mapping of tokenId -> game stats
 */
export const fetchGameStatsBatch = async (
  tokenIds: number[]
): Promise<Record<number, IGameStats>> => {
  if (!tokenIds || tokenIds.length === 0) {
    return {};
  }

  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`/api/proxy/game/batch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ids: tokenIds.map((id) => parseInt(id.toString(), 10)),
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data: IBatchGameResponse = await response.json();

      // Validate response structure
      if (!data.chickens || !Array.isArray(data.chickens)) {
        throw new Error("Invalid response format from API");
      }

      // Transform to Record<number, IGameStats>
      const statsMap: Record<number, IGameStats> = {};

      data.chickens.forEach((chicken) => {
        if (chicken && chicken.id) {
          statsMap[chicken.id] = chicken;
        }
      });

      // Fill in missing chickens with default stats
      tokenIds.forEach((tokenId) => {
        if (!statsMap[tokenId]) {
          statsMap[tokenId] = {
            id: tokenId,
            wins: 0,
            losses: 0,
            draws: 0,
            level: 1,
            state: "normal",
          };
        }
      });

      return statsMap;
    } catch (error) {
      retries++;
      console.warn(`Batch game stats fetch attempt ${retries} failed:`, error);

      if (retries >= maxRetries) {
        // Return default stats for all chickens if all retries failed
        const defaultStats: Record<number, IGameStats> = {};
        tokenIds.forEach((tokenId) => {
          defaultStats[tokenId] = {
            id: tokenId,
            wins: 0,
            losses: 0,
            draws: 0,
            level: 1,
            state: "normal",
          };
        });
        return defaultStats;
      }

      // Wait before retry
      await new Promise((resolve) => setTimeout(resolve, 1000 * retries));
    }
  }

  // This should never be reached, but TypeScript requires it
  return {};
};

/**
 * Fetch game stats for a single chicken (uses batch endpoint internally)
 * @param tokenId - Chicken token ID
 * @returns Promise with game stats
 */
export const fetchGameStats = async (tokenId: number): Promise<IGameStats> => {
  const batchResult = await fetchGameStatsBatch([tokenId]);
  return (
    batchResult[tokenId] || {
      id: tokenId,
      wins: 0,
      losses: 0,
      draws: 0,
      level: 1,
      state: "normal",
    }
  );
};

/**
 * Transform game stats to battle stats format (for backward compatibility)
 * @param gameStats - Game stats from API
 * @returns Battle stats in expected format
 */
export const transformToBattleStats = (gameStats: IGameStats): IBattleStats => {
  return {
    wins: gameStats.wins || 0,
    losses: gameStats.losses || 0,
    draws: gameStats.draws || 0,
    level: gameStats.level,
    state: gameStats.state || "normal",
    recoverDate: gameStats.recoverDate,
    stats: gameStats.stats,
  };
};

/**
 * Transform multiple game stats to battle stats format
 * @param gameStatsMap - Map of tokenId -> game stats
 * @returns Map of tokenId -> battle stats
 */
export const transformToBattleStatsMap = (
  gameStatsMap: Record<number, IGameStats>
): Record<number, IBattleStats> => {
  const battleStatsMap: Record<number, IBattleStats> = {};

  Object.entries(gameStatsMap).forEach(([tokenId, gameStats]) => {
    battleStatsMap[parseInt(tokenId, 10)] = transformToBattleStats(gameStats);
  });

  return battleStatsMap;
};
